# Comprehensive GraphQL Schema for Crypto Bubble Map Backend

# Scalar Types
scalar DateTime
scalar JSON

# Enums
enum WalletType {
  REGULAR
  EXCHANGE
  CONTRACT
  WHALE
  DEFI
  BRIDGE
  MINER
  SUSPICIOUS
}

enum RiskLevel {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum AlertSeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum AlertType {
  BALANCE_CHANGE
  HIGH_VOLUME
  RISK_INCREASE
  SUSPICIOUS_ACTIVITY
  NEW_TRANSACTION
  PHISHING_DETECTED
  SANCTIONS_MATCH
  MEV_ACTIVITY
}

enum TransactionType {
  TRANSFER
  SWAP
  MINT
  BURN
  APPROVE
  CONTRACT_CALL
  NFT_TRANSFER
}

enum TransactionStatus {
  SUCCESS
  FAILED
  PENDING
}

enum RankingCategory {
  QUALITY
  VOLUME
  ACTIVITY
  SAFETY
  NETWORK
  REPUTATION
}

enum NetworkCategory {
  LAYER1
  LAYER2
  SIDECHAIN
}

enum MoneyFlowType {
  INBOUND
  OUTBOUND
  BOTH
}

enum TransferType {
  ETH
  TOKEN
  BOTH
}

# Social Media Types
type SocialProfiles {
  twitter: String
  discord: String
  telegram: String
  github: String
  website: String
  linkedin: String
  medium: String
  reddit: String
}

# Core Wallet Types
type Wallet {
  id: ID!
  address: String!
  label: String
  balance: String
  transactionCount: Int!
  walletType: WalletType!
  riskLevel: RiskLevel!
  tags: [String!]!
  isContract: Boolean!

  # Enhanced fields for frontend
  imageUrl: String
  hasImage: Boolean
  socialProfiles: SocialProfiles
  hasVerifiedSocials: Boolean
  socialScore: Int

  # Quality metrics
  qualityScore: Int
  reputationScore: Int

  # Activity metrics
  transactionVolume: String
  averageTransactionSize: String
  activityFrequency: Float

  # Age and history
  walletAge: Int
  firstTransactionDate: DateTime
  lastTransactionDate: DateTime

  # Network metrics
  connectionCount: Int
  uniqueCounterparties: Int
  networkInfluence: Int

  # Risk indicators
  riskFlags: [String!]!
  isWhitelisted: Boolean!
  isFlagged: Boolean!

  # Performance indicators
  profitabilityScore: Int
  liquidityScore: Int
}

type WalletConnection {
  source: String!
  target: String!
  value: String!
  transactionCount: Int!
  riskLevel: RiskLevel!
  timestamp: DateTime
  type: String
}

type WalletNetwork {
  nodes: [Wallet!]!
  links: [WalletConnection!]!
  totalNodes: Int!
  totalLinks: Int!
  centerWallet: String!
}

# Risk Scoring Types
type RiskFactors {
  phishing: Int!
  mev: Int!
  laundering: Int!
  sanctions: Int!
  scam: Int!
  suspicious: Int!
}

type RiskScore {
  address: String!
  totalScore: Int!
  riskLevel: RiskLevel!
  factors: RiskFactors!
  flags: [String!]!
  lastUpdated: DateTime!
}

# Dashboard Types
type DashboardStats {
  totalWallets: Int!
  totalVolume: String!
  totalTransactions: Int!
  flaggedWallets: Int!
  whitelistedWallets: Int!
  averageQualityScore: Float!
  averageRiskScore: Float!
  recentActivity: Int!
  lastUpdate: DateTime!
}

# Input Types
input WalletNetworkInput {
  address: String!
  depth: Int = 2
  includeRiskAnalysis: Boolean = true
}

# Root Types
type Query {
  # Basic wallet queries
  wallet(address: String!): Wallet
  walletNetwork(input: WalletNetworkInput!): WalletNetwork!
  walletRiskScore(address: String!): RiskScore

  # Dashboard
  dashboardStats: DashboardStats!

  # Search
  searchWallets(query: String!, limit: Int = 20): [Wallet!]!

  # Health check
  health: String!
}

type Mutation {
  # Health check
  ping: String!
}
