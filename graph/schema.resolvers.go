package graph

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.76

import (
	"context"
	"crypto-bubble-map-be/graph/generated"
	"crypto-bubble-map-be/internal/domain/entity"
	"fmt"
	"time"
)

// Helper function to convert string to pointer
func stringPtr(s string) *string {
	return &s
}

// Helper function to convert int to pointer
func intPtr(i int) *int {
	return &i
}

// Helper function to convert time to pointer
func timePtr(t time.Time) *time.Time {
	return &t
}

// LastUpdate is the resolver for the lastUpdate field.
func (r *dashboardStatsResolver) LastUpdate(ctx context.Context, obj *entity.DashboardStats) (string, error) {
	return obj.LastUpdate.Format(time.RFC3339), nil
}

// Ping is the resolver for the ping field.
func (r *mutationResolver) Ping(ctx context.Context) (string, error) {
	return "pong", nil
}

// Wallet is the resolver for the wallet field.
func (r *queryResolver) Wallet(ctx context.Context, address string) (*entity.Wallet, error) {
	// Use the wallet repository to get real data
	wallet, err := r.walletRepo.GetWallet(ctx, address)
	if err != nil {
		return nil, fmt.Errorf("failed to get wallet: %w", err)
	}
	return wallet, nil
}

// WalletNetwork is the resolver for the walletNetwork field.
func (r *queryResolver) WalletNetwork(ctx context.Context, input entity.WalletNetworkInput) (*entity.WalletNetwork, error) {
	// Use the wallet repository to get real network data
	network, err := r.walletRepo.GetWalletNetwork(ctx, &input)
	if err != nil {
		return nil, fmt.Errorf("failed to get wallet network: %w", err)
	}
	return network, nil
}

// WalletRiskScore is the resolver for the walletRiskScore field.
func (r *queryResolver) WalletRiskScore(ctx context.Context, address string) (*entity.RiskScore, error) {
	// Use the wallet repository to get risk score
	riskScore, err := r.walletRepo.GetRiskScore(ctx, address)
	if err != nil {
		return nil, fmt.Errorf("failed to get wallet risk score: %w", err)
	}
	return riskScore, nil
}

// DashboardStats is the resolver for the dashboardStats field.
func (r *queryResolver) DashboardStats(ctx context.Context) (*entity.DashboardStats, error) {
	// Use the network repository to get real dashboard stats
	// Pass nil for networkID to get overall stats
	stats, err := r.networkRepo.GetDashboardStats(ctx, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get dashboard stats: %w", err)
	}
	return stats, nil
}

// SearchWallets is the resolver for the searchWallets field.
func (r *queryResolver) SearchWallets(ctx context.Context, query string, limit *int) ([]*entity.Wallet, error) {
	// Set default limit
	searchLimit := 20
	if limit != nil && *limit > 0 {
		searchLimit = *limit
	}

	// Use the wallet repository to search wallets
	searchResults, err := r.walletRepo.SearchWallets(ctx, query, searchLimit)
	if err != nil {
		return nil, fmt.Errorf("failed to search wallets: %w", err)
	}

	// Convert search results to wallets
	var wallets []*entity.Wallet
	for _, result := range searchResults {
		wallets = append(wallets, &result.Wallet)
	}

	return wallets, nil
}

// Health is the resolver for the health field.
func (r *queryResolver) Health(ctx context.Context) (string, error) {
	return "GraphQL API is healthy and ready!", nil
}

// LastUpdated is the resolver for the lastUpdated field.
func (r *riskScoreResolver) LastUpdated(ctx context.Context, obj *entity.RiskScore) (string, error) {
	panic(fmt.Errorf("not implemented: LastUpdated - lastUpdated"))
}

// Medium is the resolver for the medium field.
func (r *socialProfilesResolver) Medium(ctx context.Context, obj *entity.SocialProfiles) (*string, error) {
	panic(fmt.Errorf("not implemented: Medium - medium"))
}

// Reddit is the resolver for the reddit field.
func (r *socialProfilesResolver) Reddit(ctx context.Context, obj *entity.SocialProfiles) (*string, error) {
	panic(fmt.Errorf("not implemented: Reddit - reddit"))
}

// FirstTransactionDate is the resolver for the firstTransactionDate field.
func (r *walletResolver) FirstTransactionDate(ctx context.Context, obj *entity.Wallet) (*string, error) {
	panic(fmt.Errorf("not implemented: FirstTransactionDate - firstTransactionDate"))
}

// LastTransactionDate is the resolver for the lastTransactionDate field.
func (r *walletResolver) LastTransactionDate(ctx context.Context, obj *entity.Wallet) (*string, error) {
	panic(fmt.Errorf("not implemented: LastTransactionDate - lastTransactionDate"))
}

// Timestamp is the resolver for the timestamp field.
func (r *walletConnectionResolver) Timestamp(ctx context.Context, obj *entity.WalletConnection) (*string, error) {
	panic(fmt.Errorf("not implemented: Timestamp - timestamp"))
}

// TotalNodes is the resolver for the totalNodes field.
func (r *walletNetworkResolver) TotalNodes(ctx context.Context, obj *entity.WalletNetwork) (int, error) {
	panic(fmt.Errorf("not implemented: TotalNodes - totalNodes"))
}

// TotalLinks is the resolver for the totalLinks field.
func (r *walletNetworkResolver) TotalLinks(ctx context.Context, obj *entity.WalletNetwork) (int, error) {
	panic(fmt.Errorf("not implemented: TotalLinks - totalLinks"))
}

// CenterWallet is the resolver for the centerWallet field.
func (r *walletNetworkResolver) CenterWallet(ctx context.Context, obj *entity.WalletNetwork) (string, error) {
	panic(fmt.Errorf("not implemented: CenterWallet - centerWallet"))
}

// DashboardStats returns generated.DashboardStatsResolver implementation.
func (r *Resolver) DashboardStats() generated.DashboardStatsResolver {
	return &dashboardStatsResolver{r}
}

// Mutation returns generated.MutationResolver implementation.
func (r *Resolver) Mutation() generated.MutationResolver { return &mutationResolver{r} }

// Query returns generated.QueryResolver implementation.
func (r *Resolver) Query() generated.QueryResolver { return &queryResolver{r} }

// RiskScore returns generated.RiskScoreResolver implementation.
func (r *Resolver) RiskScore() generated.RiskScoreResolver { return &riskScoreResolver{r} }

// SocialProfiles returns generated.SocialProfilesResolver implementation.
func (r *Resolver) SocialProfiles() generated.SocialProfilesResolver {
	return &socialProfilesResolver{r}
}

// Wallet returns generated.WalletResolver implementation.
func (r *Resolver) Wallet() generated.WalletResolver { return &walletResolver{r} }

// WalletConnection returns generated.WalletConnectionResolver implementation.
func (r *Resolver) WalletConnection() generated.WalletConnectionResolver {
	return &walletConnectionResolver{r}
}

// WalletNetwork returns generated.WalletNetworkResolver implementation.
func (r *Resolver) WalletNetwork() generated.WalletNetworkResolver { return &walletNetworkResolver{r} }

type dashboardStatsResolver struct{ *Resolver }
type mutationResolver struct{ *Resolver }
type queryResolver struct{ *Resolver }
type riskScoreResolver struct{ *Resolver }
type socialProfilesResolver struct{ *Resolver }
type walletResolver struct{ *Resolver }
type walletConnectionResolver struct{ *Resolver }
type walletNetworkResolver struct{ *Resolver }

// !!! WARNING !!!
// The code below was going to be deleted when updating resolvers. It has been copied here so you have
// one last chance to move it out of harms way if you want. There are two reasons this happens:
//  - When renaming or deleting a resolver the old code will be put in here. You can safely delete
//    it when you're done.
//  - You have helper methods in this file. Move them out to keep these resolver files clean.
/*
	func stringPtr(s string) *string {
	return &s
}
func intPtr(i int) *int {
	return &i
}
func timePtr(t time.Time) *time.Time {
	return &t
}
*/
